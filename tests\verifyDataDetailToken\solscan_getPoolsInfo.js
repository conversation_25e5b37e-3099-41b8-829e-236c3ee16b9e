// solscan_getPoolsInfo.js
const axios = require('axios');

const DEFAULT_TOKEN = '6kjzL6NBtwhzdhZJASqHMimfaxNBGqKd2h6JHnnbRuao';

const solscan = axios.create({
  baseURL: 'https://api-v2.solscan.io',
  timeout: 30000,
  headers: {
    accept: 'application/json, text/plain, */*',
    origin: 'https://solscan.io',
    referer: 'https://solscan.io/',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  },
});

async function getPoolsInfo(address, page = 1, pageSize = 100) {
  const { data } = await solscan.get(`/v2/token/pools`, {
    params: { page, page_size: pageSize, 'token[]': address },
  });
  return data;
}

module.exports = { getPoolsInfo };

if (require.main === module) {
  const address = process.argv[2] || process.env.TOKEN_ADDRESS || DEFAULT_TOKEN;
  getPoolsInfo(address, 1, 100)
    .then((res) => console.log(JSON.stringify(res, null, 2)))
    .catch((err) => { console.error(err?.response?.data || err.message); process.exit(1); });
}
