// dex3_getTokenBasicInfo.js  (no mandatory bearer)
const axios = require('axios');

const DEFAULT_TOKEN = '6kjzL6NBtwhzdhZJASqHMimfaxNBGqKd2h6JHnnbRuao';

// Tùy chọn: nếu có thì dùng, KHÔNG bắt buộc
const DEX3_BEARER = process.env.DEX3_BEARER; // ví dụ: "Bearer eyJ..."
const DEX3_COOKIE = process.env.DEX3_COOKIE || '';

const dex3 = axios.create({
  baseURL: 'https://api.dex3.ai',
  timeout: 30000,
  headers: {
    accept: 'application/json, text/plain, */*',
    'Content-Type': 'application/json',
    origin: 'https://beta.dex3.ai',
    referer: 'https://beta.dex3.ai/',
    'user-agent':
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
    ...(DEX3_BEARER ? { authorization: DEX3_BEARER } : {}),
    ...(DEX3_COOKIE ? { Cookie: DEX3_COOKIE } : {}),
  },
});

// Retry đơn giản cho timeout/429/5xx
function shouldRetry(error) {
  return (
    error?.code === 'ECONNABORTED' ||
    [429, 500, 502, 503, 504].includes(error?.response?.status)
  );
}
const delay = (ms) => new Promise((r) => setTimeout(r, ms));

async function requestWithRetry(config, retries = 3, backoffMs = 1000) {
  try {
    const { data } = await dex3(config);
    return data;
  } catch (err) {
    if (retries > 0 && shouldRetry(err)) {
      await delay(backoffMs);
      return requestWithRetry(config, retries - 1, backoffMs * 1.5);
    }
    throw err;
  }
}

async function getTokenBasicInfo(address) {
  // Một số endpoint của DEX3 cần header động
  const headers = { clienttimestamp: Date.now().toString() };
  return requestWithRetry({
    method: 'POST',
    url: '/v2/token-detail/basic-info',
    data: { address },
    headers,
  });
}

module.exports = { getTokenBasicInfo };

if (require.main === module) {
  const address = process.argv[2] || process.env.TOKEN_ADDRESS || DEFAULT_TOKEN;

  getTokenBasicInfo(address)
    .then((res) => console.log(JSON.stringify(res, null, 2)))
    .catch((err) => {
      const status = err?.response?.status;
      const payload = err?.response?.data;
      console.error('Request failed.');
      if (status) console.error(`Status: ${status}`);
      if (payload) console.error('Response:', JSON.stringify(payload, null, 2));
      else console.error(err.message);

      // Gợi ý KHÔNG bắt buộc: nếu server yêu cầu auth
      if (status === 401 || status === 403) {
        console.error(
          'Máy chủ có thể yêu cầu xác thực. Bạn có thể cung cấp DEX3_BEARER="Bearer <token>" và/hoặc DEX3_COOKIE rồi chạy lại.'
        );
      }
      process.exit(1);
    });
}
